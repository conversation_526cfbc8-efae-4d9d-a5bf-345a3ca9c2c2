package com.common.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.common.entity.BaseEntity;
import lombok.Data;

@Data
public class KBFileTrunkInfo extends BaseEntity {
    /**
     * 主键
     */
    @TableId
    private String id;
    /**
     * 文件ID
     */
    private String fileId;
    /**
     * 知识库ID
     */
    private Integer kbId;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 内容
     */
    private String content;
    /**
     * 元数据
     */
    private String metaData;
    /**
     * 关键词
     */
    private String keywords;
    /**
     * 删除标志
     */
    private Integer delFlag;
    /**
     * 向量保存之后的ID
     */
    private String storedVectorId;
}
