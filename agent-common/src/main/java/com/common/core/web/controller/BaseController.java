package com.common.core.web.controller;

import com.common.constant.HttpStatus;
import com.common.core.web.domain.AjaxResult;
import com.common.core.web.page.PageDomain;
import com.common.core.web.page.TableDataInfo;
import com.common.core.web.page.TableSupport;
import com.common.utils.DateUtils;
import com.common.utils.StringUtils;
import com.common.utils.TextUtils;
import com.common.utils.sql.SqlUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;

import java.beans.PropertyEditorSupport;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * web层通用数据处理
 *
 * <AUTHOR>
 */
public class BaseController extends com.diboot.core.controller.BaseController {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());


    /**
     * 将前台传递过来的日期格式的字符串，自动转化为Date类型
     */
    @InitBinder
    public void initBinder(WebDataBinder binder) {
        // Date 类型转换
        binder.registerCustomEditor(Date.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) {
                setValue(DateUtils.parseDate(text));
            }
        });
    }

    /**
     * 设置请求分页数据
     */
    protected void startPage() {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize)) {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
//            Boolean reasonable = pageDomain.getReasonable();
            PageHelper.startPage(pageNum, pageSize, orderBy).setReasonable(false);
        }
    }

    /**
     * 设置请求分页数据带排序
     */
    protected void startPage(String orderByColumn, String isAsc) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        if (!TextUtils.isEmpty(orderByColumn)) {
            pageDomain.setOrderByColumn(orderByColumn);
            pageDomain.setIsAsc(isAsc);
        }
        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize)) {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            Boolean reasonable = pageDomain.getReasonable();
            PageHelper.startPage(pageNum, pageSize, orderBy).setReasonable(reasonable);
        }
    }

    /**
     * 设置请求分页数据传分页数据
     */
    protected void startPage(Integer Num ,Integer Size) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        pageDomain.setPageNum(Num);
        pageDomain.setPageSize(Size);
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize)) {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
//            Boolean reasonable = pageDomain.getReasonable();
            PageHelper.startPage(pageNum, pageSize, orderBy).setReasonable(false);
        }
    }

    /**
     * 设置请求排序数据
     */
    protected void startOrderBy() {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        if (StringUtils.isNotEmpty(pageDomain.getOrderBy())) {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            PageHelper.orderBy(orderBy);
        }
    }

    /**
     * 响应请求分页数据
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    protected TableDataInfo getDataTable(List<?> list) {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list);
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }

    /**
     * 响应请求分页数据(后台管理系统)用于组装数据后分页不正确
     */
    protected TableDataInfo getDataList(List<?> list) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        TableDataInfo rspData = new TableDataInfo();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        if (StringUtils.isNotNull(pageNum) && StringUtils.isNotNull(pageSize) && list != null) {
            List<?> collect = list.stream().skip((pageNum - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
            rspData.setRows(collect);
        }
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setTotal(list.size());
        return rspData;
    }

    /**
     * 响应请求分页数据(手写分页返回数据  APP端适用)
     * 应用：用来解决多数据组装分页返回
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    protected AjaxResult getNewDataTable(List<?> list, Map<String,Integer> map) {
        AjaxResult result = AjaxResult.success("查询成功");
        double total = map.get("total");
        double count = map.get("pageSize");
        double row = total/count;
        // 向上取整
        int ceilNum = (int) Math.ceil(row);
        boolean nextPage = map.get("pageNum") < ceilNum;
        boolean isFirstPage = map.get("pageNum") == 1;
        boolean lastPage = map.get("pageNum") == ceilNum || ceilNum == 0;
        result.put("list", list);
        result.put("hasNextPage", nextPage);
        result.put("firstPage", isFirstPage);
        result.put("lastPage", lastPage);
        result.put("total", map.get("total"));
        return result;
    }

    /**
     * 响应请求分页数据
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    protected AjaxResult getNewDataTable(List<?> list) {
        AjaxResult result = AjaxResult.success("查询成功");
        PageInfo pageInfo = new PageInfo(list);
        result.put("list", list);
        result.put("hasNextPage", pageInfo.isHasNextPage());
        result.put("firstPage", pageInfo.isIsFirstPage());
        result.put("lastPage", pageInfo.isIsLastPage());
        result.put("total", pageInfo.getTotal());
        return result;
    }

    /**
     * 返回成功
     */
    public AjaxResult success() {
        return AjaxResult.success();
    }

    /**
     * 返回成功
     */
    public AjaxResult success(Object object) {
        return AjaxResult.success(object);
    }

    /**
     * 返回成功
     */
    public AjaxResult success(String msg, Object object) {
        return AjaxResult.success(msg, object);
    }

    /**
     * 返回失败消息
     */
    public AjaxResult error() {
        return AjaxResult.error();
    }

    /**
     * 返回成功消息
     */
    public AjaxResult success(String message) {
        return AjaxResult.success(message);
    }

    /**
     * 返回失败消息
     */
    public AjaxResult error(String message) {
        return AjaxResult.error(message);
    }

    /**
     * 响应返回结果
     *
     * @param rows 影响行数
     * @return 操作结果
     */
    protected AjaxResult toAjax(int rows) {
        return rows > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 响应返回结果
     *
     * @param result 结果
     * @return 操作结果
     */
    protected AjaxResult toAjax(boolean result) {
        return result ? success() : error();
    }

    /**
     * 页面跳转
     */
    public String redirect(String url) {
        return StringUtils.format("redirect:{}", url);
    }


}
