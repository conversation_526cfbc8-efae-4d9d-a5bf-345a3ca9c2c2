package com.common.constant;

import io.jsonwebtoken.Claims;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 */
public class Constants {
    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    public static final String GBK = "GBK";

    /**
     * www主域
     */
    public static final String WWW = "www.";

    /**
     * RMI 远程方法调用
     */
    public static final String LOOKUP_RMI = "rmi:";

    /**
     * LDAP 远程方法调用
     */
    public static final String LOOKUP_LDAP = "ldap:";

    /**
     * LDAPS 远程方法调用
     */
    public static final String LOOKUP_LDAPS = "ldaps:";

    /**
     * http请求
     */
    public static final String HTTP = "http://";

    /**
     * https请求
     */
    public static final String HTTPS = "https://";

    /**
     * 通用成功标识
     */
    public static final String SUCCESS = "0";

    /**
     * 通用失败标识
     */
    public static final String FAIL = "1";

    /**
     * 登录成功状态
     */
    public static final String LOGIN_SUCCESS_STATUS = "0";

    /**
     * 登录失败状态
     */
    public static final String LOGIN_FAIL_STATUS = "1";

    /**
     * 登录成功
     */
    public static final String LOGIN_SUCCESS = "Success";

    /**
     * 注销
     */
    public static final String LOGOUT = "Logout";

    /**
     * 注册
     */
    public static final String REGISTER = "Register";

    /**
     * 登录失败
     */
    public static final String LOGIN_FAIL = "Error";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 登录用户 redis key
     */
    public static final String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * 防重提交 redis key
     */
    public static final String REPEAT_SUBMIT_KEY = "repeat_submit:";

    /**
     * 防重提交 redis key
     */
    public static final String RTC_ONLINE = "rtc_online:";

    /**
     * 限流 redis key
     */
    public static final String RATE_LIMIT_KEY = "rate_limit:";

    /**
     * 验证码有效期（分钟）
     */
    public static final Integer CAPTCHA_EXPIRATION = 10;

    /**
     * 令牌
     */
    public static final String TOKEN = "token";

    /**
     * 令牌前缀
     */
    public static final String TOKEN_PREFIX = "Bearer ";

    /**
     * 令牌前缀
     */
    public static final String LOGIN_USER_KEY = "login_user_key";

    /**
     * 用户ID
     */
    public static final String JWT_USERID = "userid";

    /**
     * 用户名称
     */
    public static final String JWT_USERNAME = Claims.SUBJECT;

    /**
     * 用户头像
     */
    public static final String JWT_AVATAR = "avatar";

    /**
     * 创建时间
     */
    public static final String JWT_CREATED = "created";

    /**
     * 用户权限
     */
    public static final String JWT_AUTHORITIES = "authorities";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 资源映射路径 前缀
     */
    public static final String RESOURCE_PREFIX = "/profile";

    /**
     * 定时任务白名单配置（仅允许访问的包名，如其他需要可以自行添加）
     */
    public static final String[] JOB_WHITELIST_STR = { "com.ruoyi" };

    /**
     * message_push 公告消息推送
     */
    public static final String message_push_notice = "message_push:notice:";
    /**
     * message_push 公告消息推送值（过期时进行取值）
     */
    public static final String message_push_notice_value = "notice:value:";

    /**
     * message_push 会议即将开始
     */
    public static final String message_push_meeting = "message_push:meeting:";

    /**
     * 定时任务违规的字符
     */
    public static final String[] JOB_ERROR_STR = {"java.net.URL", "javax.naming.InitialContext", "org.yaml.snakeyaml",
            "org.springframework.jndi"};


    /**
     * 排行榜
     */
    public static final String ranking_sort = "ranking:sort:";

    /**
     * 每日一题排行榜
     */
    public static final String st_ranking_sort = "st_ranking:sort:";
    /**
     * 法律讲堂
     */
    public static final String law_sort = "law:sort:";

    /**
     * 每日一题
     */
    public static final String ST_EXAMING = "stExaming";

    /**
     *
     */
    private static final String lawForumTypeValue="law_forum_type";

    /**
     * APP崩溃日志 redis key
     */
    public static final String APP_ERROR_LOGKEY = "app.error.logKey";

    /**
     * 企业注册订单 已读
     */
    public static final Integer READ = 1;

    /**
     * 企业注册订单 未读
     */
    public static final Integer UN_READ = 0;

    /**
     * 企业资源资源包key
     */
    public static final String COM_RESOURCE_PACKAGE = "com_resource_package:";

    /**
     * 企业资源用户消耗统计
     */
    public static final String COM_RESOURCE_USE = "com_resource_use:";

    /**
     * 企业资源用户消耗记录
     */
    public static final String COM_RESOURCE_USE_LOG = "com_resource_use_log";

    /**
     * 企业资源限制
     */
    public static final String COM_RESOURCE_LIMIT = "com_resource_limit:";

    /**
     * 用户问答限制
     */
    public static final String USER_QUESTION_COUNT = "user_question_count:";

    /**
     * 用户问答限制
     */
    public static final String USER_QUESTION_TEXT = "user_question_text:";

    /**
     * 菜单
     */
    public static final String SYS_MENU = "sys_menu:";

    /**
     * 调试终端标识
     */
    public static final String SUB_SYSTEM_DEBUG = "debugSys";


    /**
     * 平台终端标识
     */
    public static final String SUB_SYSTEM_WEB = "webSys";

    /**
     * 企业终端标识
     */
    public static final String SUB_SYSTEM_COM = "comSys";

    /**
     * 调试终端标识
     */
    public static final String ROLE_PERMISSIONS = "rolePermissions_";


    /**
     * zoom token 标识
     */
    public static final String ZOOM_TOKEN_KEY = "zoomToken";

    /**
     * zoom token 过期时间（秒）
     */
    public static final Long ZOOM_TOKEN_TIMEOUT = 3000L;

    /**
     * 验证码 redis key
     */
    public static final String PAY_PASSWORD_CODE = "pay_password_code:";

    /**
     * 极客帮 redis key 前缀
     */
    public static final String THIRD_API_GEEKBANG_CONTENT_KEY = "geekbang:content:";

    /**
     * 极客邦 课程更新lock key
     */
    public static final String UPDATE_GEEKBANG_LOCK_KEY = "updateGeekbangLock";

    /**
     * 支付密码错误次数校验
     */
    public static final String PAY_PASS_ERROR_COUNT_CHECK = "pass_error_count:";

    /**
     * 优惠类型RedisKey
     */
    public static final String DISCOUNT_DATA_KEY = "discount_data_key:";

    /**
     * 权益类型RedisKey
     */
    public static final String INTERESTS_DATA_KEY = "interests_data_key:";

    /**
     * 企业平台关联Key
     */
    public static final String COMPANY_PLATFORM_KEY = "company_platform_key:";

    /**
     * 默认业务平台ID
     */
    public static final String DEFAULT_PLATFORM_ID = "default_platform_id";

    /**
     * 业务平台数据缓存
     */
    public static final String SETTING_PLATFORM = "setting_platform:";

    /**
     * 分类平台关联Key
     */
    public static final String CHECK_CLASSIFY_PLATFORM_KEY = "check_classify_platform_key:";

    /**
     * 待办展示资源不足key
     */
    public static final String RESOURCES_MESSAGE_KEY = "resources_message_key:";

    /**
     * 待办取消展示资源不足key
     */
    public static final String DEL_RESOURCES_MESSAGE_KEY = "del_resources_message_key:";

    /**
     * 兑换券类型数据集
     */
    public static final String TR_VOUCHER_DATA = "tr_voucher_data:";

    /**
     * 分配课程
     */
    public static final String GRANT_COURSE_LOCK = "grant_course_lock:";


    /**
     * 充值订单数据集
     */
    public static final String CHECK_RECHARGE_ORDER = "check_recharge_order:";

    /**
     * 第三方数据留存
     */
    public static final String PAY_ORDER_DATA_RETENTION = "pay_order_data_retention:";

    /**
     * 知识库待解析文件Key
     */
    public static final String KNOWLEDGE_FILE_KEY = "knowledge_file:";

    /**
     * 部门信息
     */
    public static final String SYS_DEPT = "sys_dept:";

    /**
     * 线下面试二维码前缀
     */
    public static final String OFFLINE_INTERVIEW_QRCODE_EXPIRE_TIME_KEY = "offlineInterviewQrcodeExpireTime:";

    /**
     * 待办任务前缀
     */
    public static final String PENDING_TASK_KEY = "pendingTask:";

}