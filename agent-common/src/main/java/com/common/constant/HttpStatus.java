package com.common.constant;

/**
 * 返回状态码
 *
 * <AUTHOR>
 */
public class HttpStatus {
    /**
     * 操作成功
     */
    public static final int SUCCESS = 200;

    /**
     * 对象创建成功
     */
    public static final int CREATED = 201;

    /**
     * 请求已经被接受
     */
    public static final int ACCEPTED = 202;

    /**
     * 操作已经执行成功，但是没有返回数据
     */
    public static final int NO_CONTENT = 204;

    /**
     * 资源已被移除
     */
    public static final int MOVED_PERM = 301;

    /**
     * 重定向
     */
    public static final int SEE_OTHER = 303;

    /**
     * 资源没有被修改
     */
    public static final int NOT_MODIFIED = 304;

    /**
     * 参数列表错误（缺少，格式不匹配）
     */
    public static final int BAD_REQUEST = 400;

    /**
     * 未授权
     */
    public static final int UNAUTHORIZED = 401;

    /**
     * 访问受限，授权过期
     */
    public static final int FORBIDDEN = 403;

    /**
     * 资源，服务未找到
     */
    public static final int NOT_FOUND = 404;

    /**
     * 不允许的http方法
     */
    public static final int BAD_METHOD = 405;

    /**
     * 资源冲突，或者资源被锁
     */
    public static final int CONFLICT = 409;

    /**
     * 不支持的数据，媒体类型
     */
    public static final int UNSUPPORTED_TYPE = 415;
    /**
     * 未绑定公司
     */
    public static final int UNBIND_COMPANY = 416;

    /**
     * app账户是注销状态
     */
    public static final int ACCOUNT_LOGOUT = 417;
    public static final String ACCOUNT_LOGOUT_STR = "该账户正在注销中";

    /**
     * 考试过程中息屏异常状态码
     */
    public static final int EXAM_ERROR_CODE = 418;

    /**
     * 系统内部错误
     */
    public static final int ERROR = 500;

    /**
     * 接口未实现
     */
    public static final int NOT_IMPLEMENTED = 501;

    /**
     * 没有合法的数据
     */
    public static final int NO_AVAILABLE_DATA = 506;

    /**
     * 资源用尽
     */
    public static final int NO_RESOURCE = 507;

    /**
     * 轮询 支付失败code
     */

    public static final int PAY_FAILED = 508;

    /**
     * 校验 活动设置不存在重新获取
     */
    public static final int ACTIVITY_SETTING_NOT_EXIST = 509;

    /**
     * 后台管理自定义提示词
     */
    public static final int CUSTOM_TIPS = 510;

}
