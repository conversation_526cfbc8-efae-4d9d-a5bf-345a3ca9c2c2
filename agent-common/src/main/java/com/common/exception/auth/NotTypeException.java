package com.common.exception.auth;

import org.apache.commons.lang3.StringUtils;

/**
 * 未能通过的角色认证异常
 * 
 * <AUTHOR>
 */
public class NotTypeException extends RuntimeException
{
    private static final long serialVersionUID = 1L;

        public NotTypeException(String message)
    {
        super(message);
    }
//    public NotTypeException(String role)
//    {
//        super(role);
//    }
//
//    public NotTypeException(String[] roles)
//    {
//        super(StringUtils.join(roles, ","));
//    }
}
