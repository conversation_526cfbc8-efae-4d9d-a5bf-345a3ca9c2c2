package com.common.exception;

/**
 * 工具类异常
 * 
 * <AUTHOR>
 */
public class ExceptionUtils extends RuntimeException
{
    private static final long serialVersionUID = 8247610319171014183L;

    public ExceptionUtils(Throwable e)
    {
        super(e.getMessage(), e);
    }

    public ExceptionUtils(String message)
    {
        super(message);
    }

    public ExceptionUtils(String message, Throwable throwable)
    {
        super(message, throwable);
    }
}
