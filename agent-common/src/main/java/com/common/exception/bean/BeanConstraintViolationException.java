package com.common.exception.bean;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ValidationException;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Bean数据校验异常
 * <AUTHOR>
 * @date 2022-11-17
 */

public class BeanConstraintViolationException extends ValidationException {

    private final Set<ConstraintViolation<?>> constraintViolations;

    /**
     * Creates a constraint violation report.
     *
     * @param message error message
     * @param constraintViolations a {@code Set} of {@link ConstraintViolation}s or null
     */
    public BeanConstraintViolationException(String message,
                                        Set<? extends ConstraintViolation<?>> constraintViolations) {
        super( message );

        if ( constraintViolations == null ) {
            this.constraintViolations = null;
        }
        else {
            this.constraintViolations = new HashSet<>( constraintViolations );
        }
    }

    /**
     * Creates a constraint violation report.
     *
     * @param constraintViolations a {@code Set} of {@link ConstraintViolation}s or null
     */
    public BeanConstraintViolationException(Set<? extends ConstraintViolation<?>> constraintViolations) {
        this(
                constraintViolations != null ? toString( constraintViolations ) : null,
                constraintViolations
        );
    }

    /**
     * Returns the set of constraint violations reported during a validation.
     *
     * @return the {@code Set} of {@link ConstraintViolation}s or null
     */
    public Set<ConstraintViolation<?>> getConstraintViolations() {
        return constraintViolations;
    }

    private static String toString(Set<? extends ConstraintViolation<?>> constraintViolations) {
        return constraintViolations.stream()
                .map( cv -> cv == null ? "null" : cv.getMessage() )
                .collect( Collectors.joining( ", " ) );
    }
}
