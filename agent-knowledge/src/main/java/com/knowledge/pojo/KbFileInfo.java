package com.knowledge.pojo;

import lombok.Data;

import java.util.Date;

@Data
public class KbFileInfo {
    /**
     * 知识库文件ID
     */
    private Long id;
    /**
     * 知识库ID
     */
    private Long kbId;
    /**
     * 知识库文件名称
     */
    private String fileName;
    /**
     * 文件原始名称
     */
    private String fileOriginalName;
    /**
     * 文件大小
     */
    private Long fileSize;
    /**
     * 文件拓展名
     */
    private String fileExtension;
    /**
     * 是否允许下载
     */
    private Integer allowDownload;
    /**
     * 文件状态 0: 待解析, 1: 解析中, 2：解析完成
     */
    private Integer fileStatus;
    /**
     * 启用状态 0: 可用, 1: 不可用
     */
    private Integer fileEnable;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 上传时间
     */
    private Date uploadTime;
    /**
     * 分块数量
     */
    private Integer chunkCount;
    /**
     * 删除状态 0-正常，1-删除
     */
    private String delFlag;
}
