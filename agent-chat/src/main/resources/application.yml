# 应用服务 WEB 访问端口
server:
  port: 8869
# 项目配置
aio:
  # 项目名称
  name: hzyp-agent-4j
  # 项目版本
  version: 1.0.0
  # 解释
  explain: "开发环境"

spring:
  datasource:
    url: ************************************************************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
# MyBatis Plus配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml


# langchain4j 配置信息
langchain4j:
  ollama:
    chat-model:
      base-url: http://*************:11434
      model-name: qwen2.5:14b
    streaming-chat-model:
      base-url: http://*************:11434
      model-name: qwen2.5:14b
    embedding-model:
      base-url: http://*************:11434
      model-name: bge-m3
  milvus:
    host: *************
    port: 19530
    user: root
    password: milvus
    database-name: default
    collection-name: collection_tian_dev

vector_gen:
  enabled: true
  cache_dir: D:/qyd_agents/vector_gen_cache
  temp_dir: D:/qyd_agents/vector_gen_temp
  concurrency: 1


