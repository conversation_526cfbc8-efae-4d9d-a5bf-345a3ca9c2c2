

# ========== 基础目录 ==========
log_dir: "D:/qyd_agents/logs"
faiss:
  save_path: "D:/qyd_agents/faiss"

# ========== 功能开关 ==========
cross_domain: false
search_result_filtering: false
vector_gen:
  enabled: true
  cache_dir: "D:/qyd_agents/vector_gen_cache"
  temp_dir: "D:/qyd_agents/vector_gen_temp"
  concurrency: 1
pipeline:
  enabled: true
  cache_dir: "D:/qyd_agents/pipeline"

# ========== 网络请求 ==========
request:
  verify: false

# ========== LLM 配置 ==========
llm:
  chat-use: "deepseek-r1:14b"
  generate-use: "qwen2.5:14b"
  llm_list:
    - engine: "ollama"
      base_url: "http://*************:11434"
      model: "qwen2.5:14b"
      model_think: "no_think"
      model_use_web: true
      max_tokens: 32000
    - engine: "ollama"
      base_url: "http://*************:11434"
      model: "qwen3:8b"
      model_think: "all"
      model_use_web: true
      max_tokens: 40000
    - engine: "ollama"
      base_url: "http://*************:11434"
      model: "deepseek-r1:14b"
      model_think: "think"
      model_use_web: true
      max_tokens: 128000

# ========== Embedding ==========
embedding:
  name: "ollama"
  ollama:
    path: "http://*************:11434"
    model: "bge-m3"
    max_tokens: 8000
  lmstudio:
    path: "http://*************:1444/v1"
    model: "text-embedding-bge-m3"

# ========== 向量数据库 ==========
vector_db_type: "milvus"
milvus:
  host: "*************"
  port: 19530
  user: "root"
  password: "milvus"
  db_name: "default"
  collection_name: "collection_tian_dev"

# ========== 检索器 ==========
retrievers:
  ensemble:
    k: 10
    score_threshold: 0.7
    fetch_k: 10
    bm25_weight: 0.3
  vector:
    k: 5
    score_threshold: 0.7
    fetch_k: 10

# ========== Agents ==========
agents:
  memory_path: "D:/qyd_agents/memos"
  kb_agent:
    temperature: 0.1
    verb: true
    max_token_limit: 2000
  expert_agent:
    temperature: 0.3
    verb: true
    max_token_limit: 1500
  question_agent:
    temperature: 0.3
    verb: true
    max_token_limit: 3000
    mention_percent: 0.25
    min_mention_chars: 2500

# ========== 关系型数据库 ==========
mysql:
  database: "kb_dev"
  user: "root"
  password: "root"
  host: "*************"
  port: 3306
  connectionLimit: 10

sqlite:
  enabled: true
  database: "D:/qyd_agents/sqliteDB"
  db_name: "xiao_sqlite"

# ========== MinIO 对象存储 ==========
minio:
  endpoint: "*************:9000"
  access_key: "minioadmin"
  secret_key: "minioadmin"
  bucket: "bucket01"
  secure: false

# ========== Talent 模块 ==========
talent:
  talent_url: "http://localhost:8080"
  file_dir: "D:/qyd_agents/talent"
  tokens: 6144
  match_score: 60.0

