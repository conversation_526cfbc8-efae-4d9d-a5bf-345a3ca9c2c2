package com.chat.controller;

import com.common.core.AioConfig;
import com.common.core.web.domain.AjaxResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.common.core.web.domain.AjaxResult.success;

@RestController
@RequestMapping("/agent")
public class AgentController {
    /**
     * 测试
     *
     * @return
     */
    @RequestMapping("/version")
    public AjaxResult version() {
        AioConfig aioConfig = new AioConfig();
        return success(aioConfig);
    }


}
