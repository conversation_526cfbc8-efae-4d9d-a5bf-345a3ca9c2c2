package com.chat.controller;

import com.chat.service.KBConversationService;
import com.common.core.web.controller.BaseController;
import com.common.core.web.domain.AjaxResult;
import com.common.core.web.page.TableDataInfo;
import com.common.domain.KBConversation;
import com.common.domain.dto.KBConversationDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/conversation/v1")
public class KBConversationController extends BaseController {

    @Autowired
    private KBConversationService kbConversationService;

    /**
     * 会话列表
     */
    @GetMapping("/list")
    public TableDataInfo list(KBConversationDto conversationDto) {
        List<KBConversation> list = kbConversationService.selectList(conversationDto);
        return getDataList(list);
    }

    /**
     * 会话详情
     */
    @GetMapping("/detail/{id}")
    public AjaxResult detail(@PathVariable("id") Long id) {
        KBConversation kbConversation = kbConversationService.detail(id);
        return AjaxResult.success(kbConversation);
    }

    /**
     * 删除会话
     */
    @DeleteMapping("/delete/{id}")
    public AjaxResult delete(@PathVariable("id") Long id) {
        return toAjax(kbConversationService.removeById(id));
    }

    /**
     * 更新会话
     */
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody KBConversation kbConversation) {
        return toAjax(kbConversationService.update(kbConversation));
    }

    /**
     * 新建会话
     */
    @PostMapping("/new")
    public AjaxResult newConversation(@RequestBody KBConversation kbConversation) {
        return toAjax(kbConversationService.add(kbConversation));
    }

}
