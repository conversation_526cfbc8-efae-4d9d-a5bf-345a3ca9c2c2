package com.chat.controller;

import com.common.core.web.domain.AjaxResult;
import com.common.domain.dto.AgentChatDto;
import dev.langchain4j.model.chat.StreamingChatLanguageModel;
import dev.langchain4j.model.chat.response.ChatResponse;
import dev.langchain4j.model.chat.response.StreamingChatResponseHandler;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/v1")
public class ChatController {

    @Autowired
    private StreamingChatLanguageModel streamingChatModel;

    // 存放被暂停的 sessionId
    private final Set<String> pausedSessions = ConcurrentHashMap.newKeySet();

    /**
     * 请求流式对话
     */
    @GetMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> stream(AgentChatDto agentChatDto,
                               @RequestParam(value = "sessionId", required = false) String sessionId,
                               HttpServletResponse response) {
        if (sessionId == null || sessionId.trim().isEmpty()) {
            sessionId = UUID.randomUUID().toString();
        }
        final String sid = sessionId;
        response.setCharacterEncoding("UTF-8");

        return Flux.create(sink -> {
            streamingChatModel.chat(agentChatDto.getQuestion(), new StreamingChatResponseHandler() {
                @Override
                public void onPartialResponse(String partialResponse) {
                    // 如果当前 session 被暂停，立即结束
                    if (pausedSessions.contains(sid)) {
                        log.info("session {} paused, stop streaming", sid);
                        sink.complete();
                        return;
                    }
                    log.info("onPartialResponse:{}", partialResponse);
                    sink.next(partialResponse);
                }

                @Override
                public void onCompleteResponse(ChatResponse completeResponse) {
                    log.info("complete:{}", completeResponse);
                    sink.complete();
                }

                @Override
                public void onError(Throwable error) {
                    sink.error(error);
                }
            });
        });
    }

    // 停止对话输出
    @PostMapping("/pause")
    public AjaxResult pause(@RequestParam("sessionId") String sessionId) {
        pausedSessions.add(sessionId);
        log.info("session {} paused", sessionId);
        return AjaxResult.success();
    }

    // 会话详情
    @GetMapping("/detail")
    public AjaxResult detail(@RequestParam("sessionId") String sessionId) {
        return AjaxResult.success();
    }

}
