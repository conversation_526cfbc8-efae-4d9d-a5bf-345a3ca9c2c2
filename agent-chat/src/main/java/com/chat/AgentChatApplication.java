package com.chat;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@EnableAsync
@MapperScan("com.**.mapper")
public class AgentChatApplication {

    public static void main(String[] args) {
        SpringApplication.run(AgentChatApplication.class, args);

        System.out.println("AgentChatApplication 启动成功\n" +
                "               _____ ____             \n" +
                "         /\\   |_   _/ __ \\          \n" +
                "        /  \\    | || |  | |          \n" +
                "       / /\\ \\   | || |  | |         \n" +
                "      / ____ \\ _| || |__| |          \n" +
                "     /_/    \\_\\_____\\____/         \n" +
                " aio 一体机ai赋能启动 ~~~///(^v^)\\\\\\~~~ ");
    }

}
