package com.chat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chat.mapper.KBConversationMapper;
import com.chat.service.KBConversationService;
import com.common.domain.KBConversation;
import com.common.domain.dto.KBConversationDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class KBConversationServiceImpl extends ServiceImpl<KBConversationMapper, KBConversation> implements KBConversationService {

    @Autowired
    private KBConversationMapper kbConversationMapper;

    @Override
    public List<KBConversation> list(KBConversation kbConversation) {
        LambdaQueryWrapper<KBConversation> q = new LambdaQueryWrapper<>();
        q.eq(KBConversation::getGroupId, kbConversation.getGroupId());
        q.orderByDesc(KBConversation::getId);
        return baseMapper.selectList(q);
    }

    @Override
    public KBConversation detail(Long sessionId) {
        QueryWrapper<KBConversation> q = new QueryWrapper<>();
        q.eq("id", sessionId);
        return baseMapper.selectOne(q);
    }

    @Override
    public int add(KBConversation kbConversation) {
        return baseMapper.insert(kbConversation);
    }

    @Override
    public int update(KBConversation kbConversation) {
        return baseMapper.updateById(kbConversation);
    }

    @Override
    public int delete(Long id) {
        return baseMapper.deleteById(id);
    }

    @Override
    public List<KBConversation> selectList(KBConversationDto kbConversationDto) {
        LambdaQueryWrapper<KBConversation> q = new LambdaQueryWrapper<>();
        q.eq(KBConversation::getGroupId, kbConversationDto.getConversationId());
        q.orderByDesc(KBConversation::getId);
        return baseMapper.selectList(q);
    }
}
