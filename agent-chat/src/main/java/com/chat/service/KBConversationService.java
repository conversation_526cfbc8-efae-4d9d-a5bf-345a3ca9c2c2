package com.chat.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.common.domain.KBConversation;
import com.common.domain.dto.AgentChatDto;
import com.common.domain.dto.KBConversationDto;

import java.util.List;


/**
 * <AUTHOR>
 * @description 针对表【kb_conversation(知识库会话表)】的数据库操作Service
 * @createDate 2023-08-12 15:22:25
 */
public interface KBConversationService extends IService<KBConversation> {

    /**
     * 分页查询会话列表
     *
     * @param kbConversation
     * @return
     */
    List<KBConversation> list(KBConversation kbConversation);

    /**
     * 会话详情
     *
     * @param sessionId
     * @return
     */
    KBConversation detail(Long sessionId);

    /**
     * 新增会话
     *
     * @param kbConversation
     * @return
     */
    int add(KBConversation kbConversation);

    /**
     * 更新会话
     *
     * @param kbConversation
     * @return
     */
    int update(KBConversation kbConversation);

    /**
     * 删除会话
     *
     * @param id
     * @return
     */
    int delete(Long id);

    /**
     * 查询会话列表
     *
     * @param kbConversationDto
     * @return
     */
    List<KBConversation> selectList(KBConversationDto kbConversationDto);
}
