# Fix Bugs (修复问题记录)

**主要记录在项目处理的过程中遇见的错误并进行留痕，防止后续遇见类似的问题耗费时间和精力在上面处理**

 1. 类库注入问题

    ~~~
    异常信息: Field request in com.diboot.core.controller.BaseController required a bean of type 'javax.servlet.http.HttpServletRequest' that could not be found.
    解释信息: 这个报错信息乍一看是没什么问题的，因为资源库中的BaseController注入HttpServletRequest是资源类库中的处理的，并且这种问题不会提示你爆红，但是实际情况是我使用的框架是Boot3 版本，使用javax这个类库已经废弃掉了，而BaseController还在引用，才提示的报错。
    解决方案: 找到资源库‘diboot.core’的对应版本更新成最新版，进入"com.diboot.core.controller.BaseController"这个类中确认一下"HttpServletRequest"的引用路径不是javax就行（具体跟你自己框架引用的类库有关就行）。
    ~~~

    

~~~

